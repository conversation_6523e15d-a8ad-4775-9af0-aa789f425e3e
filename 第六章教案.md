# 第六章 字典教案

**授课教师：** 冯子晋
**授课时间：** 45 分钟
**授课对象：** Python 程序设计课程学生

## 授课章节

第六章 字典

- 6.1 一个简单的字典
- 6.2 使用字典
- 6.3 遍历字典
- 6.4 嵌套

## 教学目的及要求

1. **理解字典的基本概念**

   - 掌握键值对的概念和作用
   - 理解字典相比列表的优势
   - 掌握字典的创建方法

2. **掌握字典的基本操作**

   - 访问字典中的值
   - 添加、修改和删除键值对
   - 使用 get()方法安全访问

3. **熟练运用字典的遍历方法**

   - 使用 items()遍历键值对
   - 使用 keys()遍历键
   - 使用 values()遍历值

4. **理解字典的嵌套应用**
   - 字典列表的使用
   - 在字典中存储列表
   - 在字典中存储字典

## 教学重点与难点

### 重点

1. 字典的基本操作（访问、添加、修改、删除）
2. 字典的三种遍历方法
3. get()方法的使用
4. 字典与列表的选择场景

### 难点

1. 理解键值对的概念和键的唯一性
2. 掌握字典的嵌套结构操作
3. 合理选择字典还是列表来解决问题

## 教学方法

1. **对比教学法**：通过列表与字典的对比，突出字典的优势
2. **案例教学法**：使用贴近学生生活的例子（学生信息、课程成绩等）
3. **实践教学法**：边讲边练，及时巩固知识点
4. **讨论教学法**：通过问题讨论加深理解

## 教学过程设计

### 45 分钟完整课程：Python 字典

#### 第一部分：导入新课（8 分钟）

**1. 问题导入（3 分钟）**

- 提问：如何存储一个学生的多种信息？
- 展示列表的局限性：
  ```python
  student_info = ['张三', 25, '北京']  # 难以理解每个位置的含义
  ```

**2. 引出字典概念（5 分钟）**

- 字典让数据有"标签"，更清晰：
  ```python
  person = {
      'name': '张三',
      'age': 25,
      'city': '北京'
  }
  ```
- 键值对的概念：键是标签，值是内容

#### 第二部分：字典基础操作（20 分钟）

**1. 创建和访问字典（5 分钟）**

```python
person = {'name': '张三', 'age': 25, 'city': '北京'}
print(person['name'])  # 输出：张三
```

**2. 添加键值对（3 分钟）**

```python
person['gender'] = '女'
print(person['gender'])  # 输出：女
```

**3. 从空字典开始（4 分钟）**

```python
person = {}
person['name'] = '张三'
person['age'] = 25
person['city'] = '北京'
print(person)
```

**4. 修改和删除（5 分钟）**

```python
# 修改值
person['city'] = '贵阳'
print(person['city'])  # 输出：贵阳

# 删除键值对
del person['city']
print(person)
```

**5. 使用 get()方法（3 分钟）**

```python
student = {'name': '小明', 'grade': 'A'}
print(student.get('school'))            # 输出 None
print(student.get('school', '未填写'))  # 输出 '未填写'
```

#### 第三部分：课堂讨论 - 字典 vs 列表（5 分钟）

**讨论题目：**

1. 记录今天班里所有学生的出勤情况（姓名 -> 是否到场）
2. 记录你最喜欢的 5 种水果的名字
3. 存储一个游戏角色的信息，包括名字、等级、经验值、血量
4. 保存班级中所有人的身高，稍后要计算平均值

**答案分析：**

- 题目 1、3：用字典（需要标签）
- 题目 2、4：用列表（只需要顺序存储）

#### 第四部分：字典遍历（10 分钟）

**1. 遍历键值对（3 分钟）**

```python
favorite_subjects = {
    '小红': '语文',
    '小刚': '数学',
    '小丽': '英语'
}

for name, subject in favorite_subjects.items():
    print(f"{name} 喜欢 {subject}。")
```

**2. 遍历键（3 分钟）**

```python
for name in favorite_subjects.keys():
    subject = favorite_subjects[name]
    print(f"{name} 喜欢 {subject}。")
```

**3. 按顺序遍历和遍历值（4 分钟）**

```python
# 按顺序遍历键
for name in sorted(favorite_subjects.keys()):
    print(f"{name} 喜欢 {favorite_subjects[name]}。")

# 遍历所有值
for subject in favorite_subjects.values():
    print(subject)
```

#### 第五部分：字典嵌套简介（2 分钟）

**1. 字典列表**

```python
student_0 = {'name': '小红', 'score': 85}
student_1 = {'name': '小刚', 'score': 90}
students = [student_0, student_1]

for student in students:
    print(student)
```

**2. 字典中存储列表**

```python
student = {
    'name': '小明',
    'hobbies': ['篮球', '画画', '下围棋']
}

print(f"{student['name']} 的课外兴趣有：")
for hobby in student['hobbies']:
    print(f"\t{hobby}")
```

**3. 字典中存储字典**

```python
family = {
    'dad': {'name': '李大志', 'age': 45, 'hobby': '书法'},
    'mom': {'name': '王美丽', 'age': 42, 'hobby': '跳广场舞'}
}

for role, info in family.items():
    print(f"{info['name']} 是{role}，今年 {info['age']} 岁，喜欢 {info['hobby']}。")
```

#### 第六部分：课堂练习与总结（5 分钟）

**随堂小测：记录三位学生的语文成绩并计算平均分**

```python
# 学生语文成绩
scores = {'小红': 85, '小刚': 90, '小丽': 95}

total = 0

# 遍历并输出每位学生的成绩
for name, score in scores.items():
    print(f"{name} 的语文成绩是 {score} 分")
    total += score

# 计算平均成绩
average = total / len(scores)
print(f"语文平均成绩是 {average} 分")
```

**知识点回顾：**

1. 字典由键值对组成，键必须唯一
2. 字典基本操作：访问、添加、修改、删除
3. 使用 get()方法避免 KeyError
4. 三种遍历方法：items()、keys()、values()
5. 字典的嵌套应用

## 课堂小结

### 字典的核心概念

- **键值对结构**：字典由键值对组成，键必须唯一，值不必唯一
- **无序性**：字典是无序的数据结构
- **键的要求**：键必须是不可变类型（字符串、数字、元组）

### 字典的基本操作

- **访问**：`dict['key']` 或 `dict.get('key', default)`
- **添加**：`dict['new_key'] = value`
- **修改**：`dict['existing_key'] = new_value`
- **删除**：`del dict['key']`

### 字典的遍历方法

- **遍历键值对**：`for key, value in dict.items()`
- **遍历键**：`for key in dict.keys()`
- **遍历值**：`for value in dict.values()`

### 字典与列表的选择原则

- **需要标签/映射关系**：使用字典
- **只需要顺序存储**：使用列表
- **需要快速查找**：使用字典
- **需要数值计算**：使用列表

## 课后作业

### 基础练习

1. **个人信息字典**：创建一个包含你个人信息的字典（姓名、年龄、专业、爱好），并练习访问、修改操作

2. **字典遍历练习**：使用三种不同的方法遍历上面创建的字典

### 进阶练习

3. **班级成绩管理**：

   ```python
   # 创建一个班级成绩字典，包含至少5名学生的语文、数学、英语成绩
   # 实现以下功能：
   # 1. 计算每个学生的总分和平均分
   # 2. 找出每科成绩最高的学生
   # 3. 计算全班各科的平均分
   ```

4. **课程选择系统**：
   ```python
   # 创建学生选课字典，记录每个学生选择的课程列表
   # 统计每门课程的选课人数
   # 找出最受欢迎的课程
   ```

### 思考题

5. 在什么情况下你会选择使用字典而不是列表？请举出 3 个具体的例子。

6. 预习第 7 章内容，思考用户输入和 while 循环的重点难点。

## 参考资料

1. Python 官方文档 - 字典部分
2. 《Python 程序设计基础》教材第 6 章
3. 超星学习通相关习题和视频资源

## 教学反思

**本节课成功之处：**

- 通过对比教学突出了字典的优势
- 使用贴近学生生活的例子增强理解
- 循序渐进的教学设计便于学生接受

**需要改进之处：**

- 可以增加更多互动环节
- 字典嵌套部分可以安排更多练习时间
- 可以结合实际项目案例进行讲解

**学生掌握情况：**

- 基础操作掌握良好
- 遍历方法需要进一步练习
- 嵌套应用是难点，需要课后加强
